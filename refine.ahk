; Revolution Idle - Mineral Level 70 Automation Script (CORRECTED)
; This script properly drags same-level minerals together for merging

; Variables for timing and control
spawning := false
merging := false
autoMode := false

; Grid positions - adjust these to match your mineral grid
; Format: [x, y] coordinates for each grid slot
gridPositions := [[200,400], [250,400], [300,400], [350,400], [400,400]
                 ,[200,450], [250,450], [300,450], [350,450], [400,450]
                 ,[200,500], [250,500], [300,500], [350,500], [400,500]
                 ,[200,550], [250,550], [300,550], [350,550], [400,550]]

; Hotkeys
F1::
    spawning := !spawning
    if (spawning) {
        SetTimer, SpawnMinerals, 800  ; Spawn every 800ms (adjust based on cost fall)
        ToolTip, Mineral Spawning ON - Press F4 to stop
    } else {
        SetTimer, SpawnMinerals, Off
        ToolTip, Mineral Spawning OFF
    }
    SetTimer, RemoveToolTip, 2000
return

F2::
    merging := !merging
    if (merging) {
        SetTimer, MergeMinerals, 1500  ; Check for merges every 1.5 seconds
        ToolTip, Auto Merging ON - Dragging same minerals together
    } else {
        SetTimer, MergeMinerals, Off
        ToolTip, Auto Merging OFF
    }
    SetTimer, RemoveToolTip, 2000
return

F3::
    autoMode := !autoMode
    if (autoMode) {
        SetTimer, SpawnMinerals, 800
        SetTimer, MergeMinerals, 1500
        ToolTip, Full Auto Mode ON - Spawning and Merging
    } else {
        SetTimer, SpawnMinerals, Off
        SetTimer, MergeMinerals, Off
        ToolTip, Full Auto Mode OFF
    }
    SetTimer, RemoveToolTip, 2000
return

; Emergency stop
F4::
    spawning := false
    merging := false
    autoMode := false
    SetTimer, SpawnMinerals, Off
    SetTimer, MergeMinerals, Off
    ToolTip, ALL AUTOMATION STOPPED
    SetTimer, RemoveToolTip, 3000
return

; Spawn minerals function
SpawnMinerals:
    ; Click the Spawn button (adjust coordinates to your Spawn button)
    Click, 400, 300  ; Replace with actual Spawn button coordinates
    Sleep, 50  ; Small delay to prevent issues
return

; Merge minerals function - CORRECTED to drag same minerals together
MergeMinerals:
    ; This function looks for pairs of same-level minerals and drags them together
    
    ; Strategy: Check each position, then look for another mineral of the same level
    Loop, % gridPositions.Length() {
        currentPos := gridPositions[A_Index]
        
        ; Look for another mineral of the same level to merge with
        Loop, % gridPositions.Length() {
            if (A_Index = A_Index) ; Skip the same position
                continue
                
            targetPos := gridPositions[A_Index]
            
            ; Here you would ideally check if both positions have the same mineral level
            ; Since we can't easily detect mineral levels, we'll try a systematic approach
            
            ; Drag from current position to target position
            ; This attempts to merge - if they're not the same level, nothing happens
            MouseClickDrag, Left, currentPos[1], currentPos[2], targetPos[1], targetPos[2], 3
            Sleep, 200  ; Wait for merge animation/processing
            
            ; Only try one merge per cycle to avoid conflicts
            break
        }
        
        ; Small delay between checking different positions
        Sleep, 100
    }
return

; Alternative merge function - systematic grid scanning
MergeMineralsSystematic:
    ; This version systematically tries to merge adjacent positions
    ; More predictable but may be less efficient
    
    gridWidth := 5  ; Adjust to your grid width
    gridHeight := 4 ; Adjust to your grid height
    
    Loop, %gridHeight% {
        row := A_Index
        Loop, %gridWidth% {
            col := A_Index
            currentIndex := (row - 1) * gridWidth + col
            
            if (currentIndex > gridPositions.Length())
                break
                
            currentPos := gridPositions[currentIndex]
            
            ; Try to merge with the position to the right
            if (col < gridWidth) {
                rightIndex := currentIndex + 1
                if (rightIndex <= gridPositions.Length()) {
                    rightPos := gridPositions[rightIndex]
                    MouseClickDrag, Left, currentPos[1], currentPos[2], rightPos[1], rightPos[2], 3
                    Sleep, 150
                }
            }
            
            ; Try to merge with the position below
            if (row < gridHeight) {
                belowIndex := currentIndex + gridWidth
                if (belowIndex <= gridPositions.Length()) {
                    belowPos := gridPositions[belowIndex]
                    MouseClickDrag, Left, currentPos[1], currentPos[2], belowPos[1], belowPos[2], 3
                    Sleep, 150
                }
            }
        }
    }
return

; Manual merge test function
F5::
    ; Test drag between two specific positions
    MouseClickDrag, Left, 200, 400, 250, 400, 3
    ToolTip, Test merge: dragged from slot 1 to slot 2
    SetTimer, RemoveToolTip, 2000
return

; Show mouse coordinates for setup
F6::
    MouseGetPos, xpos, ypos
    ToolTip, Mouse Position: %xpos%`, %ypos% - Click on mineral positions to get coordinates
    SetTimer, RemoveToolTip, 4000
return

; Polish Prestige hotkey (manual trigger)
F7::
    ; Click Polish Prestige button (adjust coordinates)
    Click, 500, 600  ; Replace with actual Polish Prestige button coordinates
    ToolTip, Polish Prestige clicked
    SetTimer, RemoveToolTip, 2000
return

; Helper function to remove tooltips
RemoveToolTip:
    ToolTip
    SetTimer, RemoveToolTip, Off
return

; Switch to systematic merging mode
F8::
    if (merging) {
        SetTimer, MergeMinerals, Off
        SetTimer, MergeMineralsSystematic, 2000
        ToolTip, Switched to Systematic Merging Mode
    } else {
        ToolTip, Start merging first with F2, then use F8 to switch modes
    }
    SetTimer, RemoveToolTip, 3000
return